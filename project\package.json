{"name": "empirepro-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "setup": "node scripts/setup-environment.js", "env:check": "node scripts/setup-environment.js", "env:validate": "node scripts/setup-environment.js", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:security": "vitest run src/tests/security.test.ts", "test:environment": "vitest run src/tests/environment.test.ts", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:unit": "node scripts/test-runner.js --type unit", "test:integration": "node scripts/test-runner.js --type integration", "test:e2e": "node scripts/test-runner.js --type e2e", "test:performance": "node scripts/test-runner.js --type performance", "test:all": "node scripts/test-runner.js --type all --coverage", "test:ci": "node scripts/test-runner.js --type all --coverage --bail --reporter json", "test:payment": "powershell -ExecutionPolicy Bypass -File ./run-payment-tests.ps1 -TestType all -GenerateReport", "test:payment:unit": "powershell -ExecutionPolicy Bypass -File ./run-payment-tests.ps1 -TestType unit", "test:payment:integration": "powershell -ExecutionPolicy Bypass -File ./run-payment-tests.ps1 -TestType integration", "test:payment:e2e": "powershell -ExecutionPolicy Bypass -File ./run-payment-tests.ps1 -TestType e2e", "test:payment:performance": "powershell -ExecutionPolicy Bypass -File ./run-payment-tests.ps1 -TestType performance", "test:payment:security": "powershell -ExecutionPolicy Bypass -File ./run-payment-tests.ps1 -TestType security", "test:validate-setup": "node test-setup-validator.js", "security:check": "node scripts/security-check.cjs", "security:audit": "npm audit && npm run security:check && npm run test:security", "quality:check": "npm run lint && npm run test:run && npm run security:check", "quality:fix": "npm run lint -- --fix && npm run test:run", "pre-commit": "npm run quality:check", "pre-deploy": "npm run quality:check && npm run build && npm run test:coverage"}, "dependencies": {"@supabase/supabase-js": "^2.39.3", "clsx": "^2.1.0", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.2", "square": "^42.3.0", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.1.9", "@vitest/ui": "^2.1.0", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^24.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5", "vitest": "^2.1.0"}}