/**
 * Test to verify booking constraint fixes
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { User } from '@supabase/supabase-js';

// Mock Supabase
vi.mock('../lib/supabase/client', () => ({
  supabase: {
    from: vi.fn().mockReturnValue({
      insert: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: {
              id: 'booking-123',
              user_id: 'user-123',
              service_type: 'residential_regular',
              property_details: { propertyType: 'house', propertySize: 'medium' },
              service_details: { addOns: ['deep-clean'] },
              schedule: { preferredDate: '2025-12-31', preferredTime: 'morning' },
              contact: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>', phone: '5551234567' },
              status: 'pending',
              payment_status: 'pending',
              total_price: 150,
              special_instructions: 'Please be careful with antique furniture',
              metadata: { submittedAt: new Date().toISOString(), requestType: 'booking' },
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            error: null
          })
        })
      })
    })
  }
}));

// Import after mocking
import { BookingService } from '../lib/api/bookingService';

describe('Booking Constraint Fix Test', () => {
  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    created_at: new Date().toISOString(),
    app_metadata: {},
    user_metadata: {},
    aud: 'authenticated',
    role: 'authenticated'
  };

  const validFormData = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '5551234567',
    address: '123 Main St',
    city: 'Anytown',
    zipCode: '12345',
    propertyType: 'house',
    propertySize: 'medium',
    preferredDate: '2025-12-31',
    preferredTime: 'morning',
    addOns: ['deep-clean'],
    specialInstructions: 'Please be careful with antique furniture'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create booking with correct status values', async () => {
    console.log('🧪 Testing booking creation with fixed constraints...');

    const booking = await BookingService.saveBooking(
      validFormData,
      'residential_regular',
      mockUser
    );

    console.log('✅ Booking created successfully:', {
      id: booking.id,
      status: booking.status,
      payment_status: booking.payment_status,
      service_type: booking.service_type
    });

    // Verify the status values match database constraints
    expect(booking.status).toBe('pending'); // Valid status
    expect(booking.payment_status).toBe('pending'); // Valid payment status
    expect(booking.service_type).toBe('residential_regular'); // Valid service type
    expect(booking.user_id).toBe('user-123'); // Correct user ID for RLS
  });

  it('should validate status constraints', () => {
    const validation = BookingService.validateBookingData(
      validFormData,
      'residential_regular',
      mockUser
    );

    expect(validation.isValid).toBe(true);
    expect(validation.sanitizedData?.status).toBe('pending');
    expect(validation.sanitizedData?.payment_status).toBe('pending');
    
    console.log('✅ Status validation passed:', {
      status: validation.sanitizedData?.status,
      payment_status: validation.sanitizedData?.payment_status
    });
  });

  it('should handle different service types correctly', async () => {
    const allowedServiceTypes = [
      'residential_regular',
      'residential_deep', 
      'residential_move',
      'office',
      'carpet',
      'pool',
      'construction',
      'waste-management'
    ];

    for (const serviceType of allowedServiceTypes) {
      console.log(`Testing service type: ${serviceType}`);
      
      const validation = BookingService.validateBookingData(
        validFormData,
        serviceType,
        mockUser
      );

      expect(validation.isValid).toBe(true);
      expect(validation.sanitizedData?.service_type).toBe(serviceType);
      
      const booking = await BookingService.saveBooking(
        validFormData,
        serviceType,
        mockUser
      );

      expect(booking.service_type).toBe(serviceType);
      console.log(`✅ ${serviceType} booking created successfully`);
    }
  });

  it('should properly structure JSONB fields', async () => {
    const booking = await BookingService.saveBooking(
      validFormData,
      'residential_regular',
      mockUser
    );

    // Verify JSONB fields are properly structured
    expect(booking.property_details).toBeTypeOf('object');
    expect(booking.service_details).toBeTypeOf('object');
    expect(booking.schedule).toBeTypeOf('object');
    expect(booking.contact).toBeTypeOf('object');
    expect(booking.metadata).toBeTypeOf('object');

    console.log('✅ JSONB fields properly structured:', {
      property_details: Object.keys(booking.property_details),
      service_details: Object.keys(booking.service_details),
      schedule: Object.keys(booking.schedule),
      contact: Object.keys(booking.contact),
      metadata: Object.keys(booking.metadata || {})
    });
  });

  it('should include required fields for RLS', async () => {
    const booking = await BookingService.saveBooking(
      validFormData,
      'residential_regular',
      mockUser
    );

    // Verify required fields for RLS are present
    expect(booking.user_id).toBe(mockUser.id);
    expect(booking.service_type).toBeDefined();
    expect(booking.property_details).toBeDefined();
    expect(booking.service_details).toBeDefined();
    expect(booking.schedule).toBeDefined();
    expect(booking.contact).toBeDefined();

    console.log('✅ RLS required fields present:', {
      user_id: booking.user_id,
      has_property_details: !!booking.property_details,
      has_service_details: !!booking.service_details,
      has_schedule: !!booking.schedule,
      has_contact: !!booking.contact
    });
  });
});
