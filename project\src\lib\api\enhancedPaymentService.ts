import { User } from '@supabase/supabase-js';
import { supabase } from '../supabase/client';
import { <PERSON>rrorHandler } from '../utils/errorHandler';
import { InputSanitizer } from '../utils/validation';
import { StandardizedBookingData } from './bookingService';

export interface PaymentRequest {
  bookingId: string;
  amount: number;
  currency: string;
  description: string;
  customerEmail: string;
  metadata?: Record<string, unknown>;
}

export interface PaymentResponse {
  success: boolean;
  paymentLinkId: string;
  paymentUrl: string;
  paymentRecordId: string;
  error?: string;
}

export interface PaymentRecord {
  id: string;
  booking_id: string;
  payment_link_id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  square_payment_id?: string;
  customer_email: string;
  description: string;
  metadata?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

/**
 * Enhanced Payment Service with proper booking integration
 */
export class EnhancedPaymentService {
  /**
   * Create payment for a booking with proper synchronization
   */
  static async createPaymentForBooking(
    booking: StandardizedBookingData,
    amount: number,
    user: User | null
  ): Promise<PaymentResponse> {
    try {
      // Validate inputs
      if (!booking.id) {
        throw new Error('Booking ID is required for payment creation');
      }

      if (!amount || amount <= 0 || amount > 10000) {
        throw new Error('Invalid payment amount. Amount must be between $0.01 and $10,000.');
      }

      if (!user) {
        throw new Error('User authentication required for payment processing');
      }

      // Sanitize inputs
      const sanitizedAmount = InputSanitizer.sanitizeNumber(amount);
      const customerEmail = InputSanitizer.sanitizeEmail(booking.contact.email);
      const description = `${booking.service_type} - ${booking.property_details.propertyType || 'Service'}`;

      ErrorHandler.logInfo('Creating payment for booking', {
        bookingId: booking.id,
        amount: sanitizedAmount,
        serviceType: booking.service_type,
        userId: user.id
      });

      // Create payment record first
      const paymentRecord = await this.createPaymentRecord({
        bookingId: booking.id,
        amount: sanitizedAmount,
        currency: 'USD',
        description,
        customerEmail,
        metadata: {
          serviceType: booking.service_type,
          userId: user.id,
          bookingCreatedAt: booking.created_at
        }
      });

      // Create Square payment link
      const paymentLink = await this.createSquarePaymentLink(
        paymentRecord.id,
        sanitizedAmount,
        description,
        customerEmail
      );

      // Update payment record with Square payment link ID
      await this.updatePaymentRecord(paymentRecord.id, {
        payment_link_id: paymentLink.id,
        status: 'pending'
      });

      // Update booking status to pending with processing payment
      await this.updateBookingPaymentStatus(booking.id, 'pending', 'processing');

      ErrorHandler.logSuccess('Payment created successfully', {
        bookingId: booking.id,
        paymentRecordId: paymentRecord.id,
        paymentLinkId: paymentLink.id
      });

      return {
        success: true,
        paymentLinkId: paymentLink.id,
        paymentUrl: paymentLink.url,
        paymentRecordId: paymentRecord.id
      };

    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'PAYMENT_CREATION_ERROR',
        'Failed to create payment for booking',
        error
      ));

      return {
        success: false,
        paymentLinkId: '',
        paymentUrl: '',
        paymentRecordId: '',
        error: error instanceof Error ? error.message : 'Payment creation failed'
      };
    }
  }

  /**
   * Create payment record in database
   */
  private static async createPaymentRecord(request: PaymentRequest): Promise<PaymentRecord> {
    const paymentData = {
      booking_id: request.bookingId,
      amount: request.amount,
      currency: request.currency,
      status: 'pending' as const,
      customer_email: request.customerEmail,
      description: request.description,
      metadata: request.metadata || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('payment_records')
      .insert([paymentData])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create payment record: ${error.message}`);
    }

    return data as PaymentRecord;
  }

  /**
   * Create Square payment link
   */
  private static async createSquarePaymentLink(
    paymentRecordId: string,
    amount: number,
    description: string,
    customerEmail: string
  ): Promise<{ id: string; url: string }> {
    try {
      // Call Square API to create payment link
      const response = await fetch('/api/create-payment-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: Math.round(amount * 100), // Convert to cents
          description,
          customerEmail,
          orderId: paymentRecordId,
          metadata: {
            paymentRecordId,
            source: 'web_booking'
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Square API error: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success || !result.paymentLink) {
        throw new Error(result.error || 'Failed to create Square payment link');
      }

      return {
        id: result.paymentLink.id,
        url: result.paymentLink.url
      };

    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'SQUARE_API_ERROR',
        'Failed to create Square payment link',
        error
      ));
      throw error;
    }
  }

  /**
   * Update payment record
   */
  private static async updatePaymentRecord(
    paymentRecordId: string,
    updates: Partial<PaymentRecord>
  ): Promise<void> {
    const { error } = await supabase
      .from('payment_records')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', paymentRecordId);

    if (error) {
      throw new Error(`Failed to update payment record: ${error.message}`);
    }
  }

  /**
   * Update booking payment status
   */
  private static async updateBookingPaymentStatus(
    bookingId: string,
    status: StandardizedBookingData['status'],
    paymentStatus: StandardizedBookingData['payment_status']
  ): Promise<void> {
    const { error } = await supabase
      .from('booking_forms')
      .update({
        status,
        payment_status: paymentStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId);

    if (error) {
      throw new Error(`Failed to update booking status: ${error.message}`);
    }
  }

  /**
   * Handle payment webhook status update
   */
  static async handlePaymentStatusUpdate(
    paymentLinkId: string,
    status: 'completed' | 'failed' | 'cancelled',
    squarePaymentId?: string
  ): Promise<void> {
    try {
      // Find payment record
      const { data: paymentRecord, error: paymentError } = await supabase
        .from('payment_records')
        .select('*')
        .eq('payment_link_id', paymentLinkId)
        .single();

      if (paymentError || !paymentRecord) {
        throw new Error(`Payment record not found for link ID: ${paymentLinkId}`);
      }

      // Update payment record
      await this.updatePaymentRecord(paymentRecord.id, {
        status,
        square_payment_id: squarePaymentId,
        metadata: {
          ...paymentRecord.metadata,
          statusUpdatedAt: new Date().toISOString(),
          squarePaymentId
        }
      });

      // Update booking status based on payment status
      let bookingStatus: StandardizedBookingData['status'];
      let paymentStatusValue: StandardizedBookingData['payment_status'];

      switch (status) {
        case 'completed':
          bookingStatus = 'confirmed';
          paymentStatusValue = 'completed';
          break;
        case 'failed':
        case 'cancelled':
          bookingStatus = 'cancelled';
          paymentStatusValue = 'failed';
          break;
        default:
          bookingStatus = 'pending';
          paymentStatusValue = 'pending';
      }

      await this.updateBookingPaymentStatus(
        paymentRecord.booking_id,
        bookingStatus,
        paymentStatusValue
      );

      ErrorHandler.logInfo('Payment status updated successfully', {
        paymentLinkId,
        status,
        bookingId: paymentRecord.booking_id
      });

    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'PAYMENT_STATUS_UPDATE_ERROR',
        'Failed to handle payment status update',
        error
      ));
      throw error;
    }
  }

  /**
   * Get payment status for booking
   */
  static async getPaymentStatus(bookingId: string): Promise<PaymentRecord | null> {
    try {
      const { data, error } = await supabase
        .from('payment_records')
        .select('*')
        .eq('booking_id', bookingId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No payment record found
          return null;
        }
        throw error;
      }

      return data as PaymentRecord;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'PAYMENT_STATUS_FETCH_ERROR',
        'Failed to fetch payment status',
        error
      ));
      return null;
    }
  }
}
