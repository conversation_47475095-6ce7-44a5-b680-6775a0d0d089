import { supabase, isSupabaseConfigured } from '../supabase/client';
import type { FormData } from '../../pages/ServiceForm/forms/carpet/types';
import type { User } from '@supabase/supabase-js';
import { ErrorHandler } from '../utils/errorHandler';

// Secure webhook configuration
const WEBHOOK_CONFIG = {
  url: import.meta.env.VITE_N8N_WEBHOOK_URL || '',
  timeout: 10000, // 10 seconds
  retries: 3
};

// Validate webhook configuration
const isWebhookConfigured = Boolean(WEBHOOK_CONFIG.url && WEBHOOK_CONFIG.url.startsWith('https://'));

if (!isWebhookConfigured && import.meta.env.PROD) {
  console.warn('⚠️ N8N webhook URL not configured. Set VITE_N8N_WEBHOOK_URL environment variable.');
}

// Secure webhook sender with retry logic and timeout
async function sendSecureWebhook(payload: any, retryCount = 0): Promise<boolean> {
  if (!isWebhookConfigured) {
    console.warn('Webhook not configured, skipping notification');
    return false;
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), WEBHOOK_CONFIG.timeout);

    const response = await fetch(WEBHOOK_CONFIG.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'EmpirePro-Booking-System/1.0'
      },
      body: JSON.stringify(payload),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Webhook failed with status: ${response.status}`);
    }

    console.log('Webhook notification sent successfully');
    return true;
  } catch (error) {
    console.error(`Webhook attempt ${retryCount + 1} failed:`, error);

    // Retry logic
    if (retryCount < WEBHOOK_CONFIG.retries - 1) {
      const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
      console.log(`Retrying webhook in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return sendSecureWebhook(payload, retryCount + 1);
    }

    console.error('All webhook retry attempts failed');
    return false;
  }
}

export async function submitCarpetRequest(formData: FormData, user: User | null) {
  try {
    // First check if user exists
    if (!user) {
      throw new Error('User must be authenticated to submit a request');
    }

    if (!isSupabaseConfigured) {
      throw new Error('Database connection not configured. Please connect to Supabase first.');
    }

    // Log the request data
    ErrorHandler.logInfo('Submitting carpet request', {
      userId: user.id,
      formData
    });

    const { data, error } = await supabase
      .from('booking_forms')
      .insert([{
        user_id: user.id,
        service_type: 'carpet',
        property_details: formData.propertyDetails,
        service_details: {
          carpetDetails: formData.carpetDetails,
          stainTreatment: formData.stainTreatment
        },
        schedule: formData.schedule,
        contact: formData.contact,
        status: 'pending'
      }])
      .select();

    if (error) {
      ErrorHandler.logError(ErrorHandler.createError('SUPABASE_ERROR', 'Supabase error occurred', error));
      
      // Check if the error is related to the webhook
      if (error.message && (
        error.message.includes('http_post') || 
        error.message.includes('webhook') ||
        error.message.includes('extension "http"')
      )) {
        // Try to fetch the booking to confirm it was created
        const { data: bookingData, error: fetchError } = await supabase
          .from('booking_forms')
          .select()
          .match({ 
            user_id: user.id,
            service_type: 'carpet'
          })
          .order('created_at', { ascending: false })
          .limit(1);
          
        if (!fetchError && bookingData && bookingData.length > 0) {
          // Booking was created successfully despite webhook error
          ErrorHandler.logSuccess('Booking created successfully despite webhook error', bookingData[0]);
          
          // Try to manually send the webhook
          try {
            // Format the data according to the required format
            const webhookPayload = {
              id: bookingData[0].id,
              status: 'sent',
              service_type: bookingData[0].service_type,
              property_details: bookingData[0].property_details,
              schedule: bookingData[0].schedule,
              contact: bookingData[0].contact,
              created_at: bookingData[0].created_at
            };
            
            const webhookSuccess = await sendSecureWebhook(webhookPayload);

            if (webhookSuccess) {
              ErrorHandler.logSuccess('Manual webhook notification sent successfully');
            } else {
              ErrorHandler.logError(ErrorHandler.createError('WEBHOOK_ERROR', 'Manual webhook notification failed after all retries'));
            }
          } catch (webhookError) {
            ErrorHandler.logError(ErrorHandler.createError('WEBHOOK_ERROR', 'Failed to send manual webhook notification', webhookError));
          }
          
          return bookingData[0];
        }
        
        // If we couldn't confirm the booking was created, show a more helpful error
        throw new Error('Your booking was received, but we encountered an issue with our notification system. Your booking is still being processed. You can check your dashboard for updates.');
      }
      
      throw new Error(error.message || 'Failed to submit request');
    }

    if (!data || data.length === 0) {
      throw new Error('No data returned from booking submission');
    }

    // If the booking was created successfully, try to manually send the webhook as a backup
    try {
      // Format the data according to the required format
      const webhookPayload = {
        id: data[0].id,
        status: 'sent',
        service_type: data[0].service_type,
        property_details: data[0].property_details,
        schedule: data[0].schedule,
        contact: data[0].contact,
        created_at: data[0].created_at
      };
      
      const webhookSuccess = await sendSecureWebhook(webhookPayload);

      if (webhookSuccess) {
        ErrorHandler.logSuccess('Backup webhook notification sent successfully');
      } else {
        ErrorHandler.logError(ErrorHandler.createError('WEBHOOK_ERROR', 'Backup webhook notification failed after all retries'));
      }
    } catch (webhookError) {
      ErrorHandler.logError(ErrorHandler.createError('WEBHOOK_ERROR', 'Failed to send backup webhook notification', webhookError));
      // Don't throw an error here, as the booking was still created successfully
    }

    ErrorHandler.logSuccess('Request submitted successfully', data[0]);
    return data[0];
  } catch (error) {
    ErrorHandler.logError(ErrorHandler.createError('CARPET_REQUEST_ERROR', 'Error submitting carpet request', error));
    throw error;
  }
}

export async function getCarpetRequests(userId: string) {
  try {
    if (!isSupabaseConfigured) {
      throw new Error('Database connection not configured. Please connect to Supabase first.');
    }
    
    ErrorHandler.logInfo('Fetching carpet requests for user', userId);
    
    const { data, error } = await supabase
      .from('booking_forms')
      .select('*')
      .eq('user_id', userId)
      .eq('service_type', 'carpet')
      .order('created_at', { ascending: false });

    if (error) {
      ErrorHandler.logError(ErrorHandler.createError('SUPABASE_ERROR', 'Supabase error occurred', error));
      throw new Error(error.message || 'Failed to fetch requests');
    }

    ErrorHandler.logSuccess('Fetched requests', data);
    return data;
  } catch (error) {
    ErrorHandler.logError(ErrorHandler.createError('CARPET_REQUEST_FETCH_ERROR', 'Error fetching carpet requests', error));
    throw error;
  }
}