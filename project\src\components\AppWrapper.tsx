import React from 'react';
import { RouterProvider } from 'react-router-dom';
import type { Router } from '@remix-run/router';
import { PaymentNotificationProvider } from '../hooks/usePaymentNotification';
import { PaymentSuccessPopup } from './PaymentSuccessPopup';
import { EnvironmentChecker } from './debug/EnvironmentChecker';

interface AppWrapperProps {
  router: Router;
}

export const AppWrapper: React.FC<AppWrapperProps> = ({ router }) => {
  return (
    <>
      <RouterProvider router={router} />
      
      {/* Payment system wrapper - outside router but payment popup won't use navigation */}
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
        {/* Only show environment checker in development for security */}
        {!import.meta.env.PROD && <EnvironmentChecker />}
      </PaymentNotificationProvider>
    </>
  );
}; 