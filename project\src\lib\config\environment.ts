/**
 * Environment Configuration and Validation
 * 
 * This module provides centralized environment variable management
 * with validation, type safety, and security checks.
 */

export interface EnvironmentConfig {
  // Supabase Configuration
  supabase: {
    url: string;
    anonKey: string;
    isConfigured: boolean;
  };
  
  // Square Payment Configuration
  square: {
    applicationId: string;
    accessToken: string;
    locationId: string;
    environment: 'sandbox' | 'production';
    isConfigured: boolean;
  };
  
  // Webhook Configuration
  webhooks: {
    n8nUrl: string;
    isConfigured: boolean;
  };
  
  // Application Configuration
  app: {
    environment: 'development' | 'production';
    isDevelopment: boolean;
    isProduction: boolean;
  };
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

class EnvironmentValidator {
  private static instance: EnvironmentValidator;
  private config: EnvironmentConfig | null = null;

  private constructor() {}

  public static getInstance(): EnvironmentValidator {
    if (!EnvironmentValidator.instance) {
      EnvironmentValidator.instance = new EnvironmentValidator();
    }
    return EnvironmentValidator.instance;
  }

  /**
   * Load and validate environment configuration
   */
  public loadConfig(): EnvironmentConfig {
    if (this.config) {
      return this.config;
    }

    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
    const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
    const squareAppId = import.meta.env.VITE_SQUARE_APPLICATION_ID || '';
    const squareToken = import.meta.env.VITE_SQUARE_ACCESS_TOKEN || '';
    const squareLocationId = import.meta.env.VITE_SQUARE_LOCATION_ID || '';
    const squareEnv = import.meta.env.VITE_SQUARE_ENVIRONMENT || 'sandbox';
    const n8nUrl = import.meta.env.VITE_N8N_WEBHOOK_URL || '';

    this.config = {
      supabase: {
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        isConfigured: this.validateSupabaseConfig(supabaseUrl, supabaseAnonKey)
      },
      square: {
        applicationId: squareAppId,
        accessToken: squareToken,
        locationId: squareLocationId,
        environment: squareEnv as 'sandbox' | 'production',
        isConfigured: this.validateSquareConfig(squareAppId, squareToken, squareLocationId)
      },
      webhooks: {
        n8nUrl: n8nUrl,
        isConfigured: this.validateWebhookConfig(n8nUrl)
      },
      app: {
        environment: import.meta.env.PROD ? 'production' : 'development',
        isDevelopment: !import.meta.env.PROD,
        isProduction: import.meta.env.PROD
      }
    };

    return this.config;
  }

  /**
   * Validate Supabase configuration
   */
  private validateSupabaseConfig(url: string, key: string): boolean {
    return Boolean(
      url && 
      url.startsWith('https://') && 
      url.includes('.supabase.co') &&
      key && 
      key.length > 100 &&
      key.startsWith('eyJ')
    );
  }

  /**
   * Validate Square configuration
   */
  private validateSquareConfig(appId: string, token: string, locationId: string): boolean {
    const validAppId = Boolean(
      appId && 
      (appId.startsWith('sandbox-sq0idb-') || appId.startsWith('sq0idb-'))
    );
    
    const validToken = Boolean(
      token && 
      token.startsWith('EAAAl') && 
      token.length > 50
    );
    
    const validLocationId = Boolean(locationId && locationId.length > 10);

    return validAppId && validToken && validLocationId;
  }

  /**
   * Validate webhook configuration
   */
  private validateWebhookConfig(url: string): boolean {
    return Boolean(
      url && 
      url.startsWith('https://') && 
      url.includes('webhook')
    );
  }

  /**
   * Perform comprehensive validation
   */
  public validate(): ValidationResult {
    const config = this.loadConfig();
    const errors: string[] = [];
    const warnings: string[] = [];

    // Supabase validation
    if (!config.supabase.isConfigured) {
      errors.push('Supabase configuration is missing or invalid');
    }

    // Square validation
    if (!config.square.isConfigured) {
      if (config.app.isProduction) {
        errors.push('Square payment configuration is required in production');
      } else {
        warnings.push('Square payment configuration is not set up');
      }
    }

    // Environment-specific checks
    if (config.app.isProduction) {
      if (config.square.environment === 'sandbox') {
        errors.push('Production environment should not use Square sandbox');
      }
    }

    // Webhook validation - only warn in production
    if (!config.webhooks.isConfigured && config.app.isProduction) {
      warnings.push('Webhook notifications are not configured');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get configuration summary for debugging
   */
  public getConfigSummary(): Record<string, any> {
    const config = this.loadConfig();
    
    return {
      supabase: {
        configured: config.supabase.isConfigured,
        url: config.supabase.url ? 'Set' : 'Missing'
      },
      square: {
        configured: config.square.isConfigured,
        environment: config.square.environment,
        applicationId: config.square.applicationId ? 'Set' : 'Missing'
      },
      webhooks: {
        configured: config.webhooks.isConfigured
      },
      app: {
        environment: config.app.environment
      }
    };
  }
}

// Export singleton instance
export const environmentValidator = EnvironmentValidator.getInstance();

// Export convenience functions
export function getEnvironmentConfig(): EnvironmentConfig {
  return environmentValidator.loadConfig();
}

export function validateEnvironment(): ValidationResult {
  return environmentValidator.validate();
}

export function getConfigSummary(): Record<string, any> {
  return environmentValidator.getConfigSummary();
}

// Initialize and validate on module load
const validation = validateEnvironment();
if (!validation.isValid) {
  console.error('❌ Environment validation failed:', validation.errors);
}
if (validation.warnings.length > 0) {
  console.warn('⚠️ Environment warnings:', validation.warnings);
}
