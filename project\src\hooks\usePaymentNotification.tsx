import React, { createContext, useContext, useState, useEffect } from 'react';
import { <PERSON>rror<PERSON>and<PERSON> } from '../lib/utils/errorHandler';

interface PaymentNotificationContextType {
  showSuccessPopup: boolean;
  hideSuccessPopup: () => void;
  onPaymentSuccess: () => void;
}

const PaymentNotificationContext = createContext<PaymentNotificationContextType | undefined>(undefined);

export const PaymentNotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);

  const checkForPaymentSuccess = () => {
    ErrorHandler.logInfo('Checking for payment success', {
      url: window.location.href,
      timestamp: new Date().toISOString()
    });

    // Check URL parameters for payment success
    const urlParams = new URLSearchParams(window.location.search);
    const paymentSuccess = urlParams.get('payment_success');

    if (paymentSuccess === 'true') {
      ErrorHandler.logSuccess('Payment success detected from URL', {
        originalUrl: window.location.href,
        timestamp: new Date().toISOString()
      });
      setShowSuccessPopup(true);

      // Clean URL after showing popup
      const cleanUrl = window.location.pathname;
      window.history.replaceState({}, document.title, cleanUrl);
      return true;
    }

    // Also check localStorage for payment tracking (Square redirect)
    const paymentTracking = localStorage.getItem('paymentTracking');
    if (paymentTracking) {
      try {
        const tracking = JSON.parse(paymentTracking);
        const createdTime = tracking.createdAt || tracking.startTime || 0;
        const timeSincePayment = Date.now() - createdTime;
        
        // If payment was initiated recently (within 1 hour), show success
        if (timeSincePayment < 3600000) { // 1 hour
          ErrorHandler.logSuccess('Recent payment detected from localStorage', {
            timeSincePayment,
            trackingData: tracking,
            timestamp: new Date().toISOString()
          });
          setShowSuccessPopup(true);
          localStorage.removeItem('paymentTracking'); // Clean up
          return true;
        }
      } catch (error) {
        ErrorHandler.logError(ErrorHandler.createError('PAYMENT_TRACKING_ERROR', 'Error parsing payment tracking', error));
        localStorage.removeItem('paymentTracking');
      }
    }
    
    return false;
  };

  useEffect(() => {
    ErrorHandler.logInfo('Payment notification system starting', {
      timestamp: new Date().toISOString(),
      location: window.location.pathname
    });

    // Initial check
    checkForPaymentSuccess();

    // Listen for URL changes (back/forward buttons, programmatic changes)
    const handlePopState = () => {
      ErrorHandler.logInfo('URL changed, checking for payment success', {
        newPath: window.location.pathname,
        timestamp: new Date().toISOString()
      });
      checkForPaymentSuccess();
    };

    // Listen for custom payment success events
    const handleCustomPaymentSuccess = (event: any) => {
      ErrorHandler.logInfo('Custom payment success event received', {
        detail: event?.detail || 'No detail provided',
        timestamp: new Date().toISOString()
      });
      setShowSuccessPopup(true);
    };
    
    // Add event listeners
    window.addEventListener('popstate', handlePopState);
    window.addEventListener('paymentSuccess', handleCustomPaymentSuccess);
    
    // Cleanup
    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('paymentSuccess', handleCustomPaymentSuccess);
    };
  }, []);

  const hideSuccessPopup = () => {
    setShowSuccessPopup(false);
  };

  const onPaymentSuccess = () => {
    ErrorHandler.logSuccess('Manual payment success triggered');
    setShowSuccessPopup(true);
  };

  return (
    <PaymentNotificationContext.Provider value={{ showSuccessPopup, hideSuccessPopup, onPaymentSuccess }}>
      {children}
    </PaymentNotificationContext.Provider>
  );
};

export const usePaymentNotification = () => {
  const context = useContext(PaymentNotificationContext);
  if (context === undefined) {
    throw new Error('usePaymentNotification must be used within a PaymentNotificationProvider');
  }
  return context;
}; 