/**
 * PWA Utilities for Empire Pro Cleaning
 * Handles service worker registration, update notifications, and PWA features
 */

export interface PWAInstallPrompt {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

class PWAManager {
  private installPrompt: PWAInstallPrompt | null = null;
  private registration: ServiceWorkerRegistration | null = null;

  /**
   * Initialize PWA features
   */
  async init(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        await this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupUpdateNotifications();
      } catch (error) {
        console.error('PWA initialization failed:', error);
      }
    }
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<void> {
    try {
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('Service Worker registered successfully');

      // Handle updates
      this.registration.addEventListener('updatefound', () => {
        const newWorker = this.registration?.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              this.showUpdateNotification();
            }
          });
        }
      });

    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }

  /**
   * Setup install prompt handling
   */
  private setupInstallPrompt(): void {
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      this.installPrompt = e as any;
      this.showInstallButton();
    });

    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed');
      this.hideInstallButton();
      this.installPrompt = null;
    });
  }

  /**
   * Show install button
   */
  private showInstallButton(): void {
    // Create install button if it doesn't exist
    if (!document.getElementById('pwa-install-button')) {
      const button = document.createElement('button');
      button.id = 'pwa-install-button';
      button.innerHTML = '📱 Install App';
      button.className = 'fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 hover:bg-blue-700 transition-colors';
      button.onclick = () => this.promptInstall();
      document.body.appendChild(button);
    }
  }

  /**
   * Hide install button
   */
  private hideInstallButton(): void {
    const button = document.getElementById('pwa-install-button');
    if (button) {
      button.remove();
    }
  }

  /**
   * Prompt user to install PWA
   */
  async promptInstall(): Promise<void> {
    if (!this.installPrompt) return;

    try {
      await this.installPrompt.prompt();
      const { outcome } = await this.installPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
      
      this.installPrompt = null;
      this.hideInstallButton();
    } catch (error) {
      console.error('Install prompt failed:', error);
    }
  }

  /**
   * Show update notification
   */
  private showUpdateNotification(): void {
    // Create update notification
    const notification = document.createElement('div');
    notification.id = 'pwa-update-notification';
    notification.innerHTML = `
      <div class="fixed top-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm">
        <p class="mb-2">🔄 App update available!</p>
        <div class="flex gap-2">
          <button id="pwa-update-button" class="bg-white text-green-600 px-3 py-1 rounded text-sm hover:bg-gray-100">
            Update Now
          </button>
          <button id="pwa-dismiss-button" class="bg-green-700 text-white px-3 py-1 rounded text-sm hover:bg-green-800">
            Later
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(notification);

    // Handle update button click
    document.getElementById('pwa-update-button')?.addEventListener('click', () => {
      this.applyUpdate();
    });

    // Handle dismiss button click
    document.getElementById('pwa-dismiss-button')?.addEventListener('click', () => {
      notification.remove();
    });
  }

  /**
   * Apply service worker update
   */
  private applyUpdate(): void {
    if (this.registration?.waiting) {
      this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  }

  /**
   * Check if app is installed
   */
  isInstalled(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true;
  }

  /**
   * Get installation status
   */
  getInstallationStatus(): 'installed' | 'installable' | 'not-supported' {
    if (this.isInstalled()) return 'installed';
    if (this.installPrompt) return 'installable';
    return 'not-supported';
  }

  /**
   * Request persistent storage
   */
  async requestPersistentStorage(): Promise<boolean> {
    if ('storage' in navigator && 'persist' in navigator.storage) {
      try {
        const persistent = await navigator.storage.persist();
        console.log('Persistent storage:', persistent);
        return persistent;
      } catch (error) {
        console.error('Persistent storage request failed:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Get storage usage
   */
  async getStorageUsage(): Promise<{ used: number; quota: number } | null> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate();
        return {
          used: estimate.usage || 0,
          quota: estimate.quota || 0
        };
      } catch (error) {
        console.error('Storage estimate failed:', error);
        return null;
      }
    }
    return null;
  }
}

// Export singleton instance
export const pwaManager = new PWAManager();

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  pwaManager.init();
}
