import { supabase, isSupabaseConfigured } from '../supabase/client';
import type { User } from '@supabase/supabase-js';
import { <PERSON>rrorHandler } from '../utils/errorHandler';
import { FormValidator, ValidationError, InputSanitizer } from '../utils/validation';
import { validateRequest } from './security';

// Secure webhook configuration
const WEBHOOK_CONFIG = {
  url: import.meta.env.VITE_N8N_WEBHOOK_URL || '',
  timeout: 10000, // 10 seconds
  retries: 3
};

// Validate webhook configuration
const isWebhookConfigured = Boolean(WEBHOOK_CONFIG.url && WEBHOOK_CONFIG.url.startsWith('https://'));

if (!isWebhookConfigured && import.meta.env.PROD) {
  console.warn('⚠️ N8N webhook URL not configured. Set VITE_N8N_WEBHOOK_URL environment variable.');
}

// Secure webhook sender with retry logic and timeout
async function sendSecureWebhook(payload: any, retryCount = 0): Promise<boolean> {
  if (!isWebhookConfigured) {
    console.warn('Webhook not configured, skipping notification');
    return false;
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), WEBHOOK_CONFIG.timeout);

    const response = await fetch(WEBHOOK_CONFIG.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'EmpirePro-Booking-System/1.0'
      },
      body: JSON.stringify(payload),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Webhook failed with status: ${response.status}`);
    }

    console.log('Webhook notification sent successfully');
    return true;
  } catch (error) {
    console.error(`Webhook attempt ${retryCount + 1} failed:`, error);

    // Retry logic
    if (retryCount < WEBHOOK_CONFIG.retries - 1) {
      const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
      console.log(`Retrying webhook in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return sendSecureWebhook(payload, retryCount + 1);
    }

    console.error('All webhook retry attempts failed');
    return false;
  }
}

export async function submitBookingForm(formData: any, serviceType: string, user: User | null) {
  try {
    if (!isSupabaseConfigured) {
      const error = ErrorHandler.createError('SUPABASE_NOT_CONFIGURED', 'Database connection not configured. Please connect to Supabase first.');
      ErrorHandler.logError(error);
      throw new Error(error.message);
    }

    // Security validation
    await validateRequest({
      method: 'POST',
      url: '/api/booking-forms',
      headers: {},
      body: formData,
      userId: user?.id,
      timestamp: Date.now()
    });

    // Sanitize form data
    const sanitizedFormData = sanitizeFormData(formData);

    // Validate sanitized form data
    const validationErrors = validateFormData(sanitizedFormData);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.map(e => e.message).join(', ')}`);
    }

    if (!supabase) {
      const error = ErrorHandler.createError('SUPABASE_CLIENT_ERROR', 'Supabase client is not initialized');
      ErrorHandler.logError(error);
      throw new Error(error.message);
    }

    // Validate form data based on service type
    const validationRules = FormValidator.getServiceRules(serviceType);
    if (Object.keys(validationRules).length > 0) {
      const validationErrors = FormValidator.validateForm(formData, validationRules);
      
      if (validationErrors.length > 0) {
        const errorMessages = validationErrors.map(err => err.message).join(', ');
        const error = ErrorHandler.createError('VALIDATION_ERROR', `Please fix the following errors: ${errorMessages}`, validationErrors);
        ErrorHandler.logError(error);
        throw new Error(error.message);
      }
    }

    // Prepare the request data using sanitized form data
    const requestData = {
      user_id: user?.id || null,
      service_type: serviceType,
      property_details: sanitizedFormData.propertyDetails || {},
      service_details: getServiceSpecificDetails(sanitizedFormData, serviceType),
      schedule: sanitizedFormData.schedule || {},
      contact: sanitizedFormData.contact || {},
      status: 'pending'
    };

    ErrorHandler.logInfo('Submitting booking form', requestData);

    // Insert the booking using Supabase client
    const { data, error } = await supabase
      .from('booking_forms')
      .insert([requestData])
      .select();

    if (error) {
      ErrorHandler.logError(ErrorHandler.createError('SUPABASE_ERROR', 'Supabase error occurred', error));
      
      // Handle Supabase-specific errors
      const supabaseError = ErrorHandler.handleSupabaseError(error);
      ErrorHandler.logError(supabaseError);
      
      // Check if the error is related to the http_post function
      if (error.message && (
        error.message.includes('http_post') || 
        error.message.includes('webhook') ||
        error.message.includes('extension "http"')
      )) {
        // The booking was likely created but the webhook notification failed
        // Try to fetch the booking to confirm
        const { data: bookingData, error: fetchError } = await supabase
          .from('booking_forms')
          .select()
          .match({ 
            user_id: user?.id,
            service_type: serviceType
          })
          .order('created_at', { ascending: false })
          .limit(1);
          
        if (!fetchError && bookingData && bookingData.length > 0) {
          // Booking was created successfully despite webhook error
          ErrorHandler.logSuccess('Booking created successfully despite webhook error', bookingData[0]);

          // Try to manually send the webhook
          try {
            await sendWebhookNotification(bookingData[0]);
            ErrorHandler.logSuccess('Manual webhook notification sent successfully');
          } catch (webhookError) {
            ErrorHandler.logError(ErrorHandler.createError('WEBHOOK_ERROR', 'Failed to send manual webhook notification', webhookError));
          }
          
          return bookingData[0];
        }
        
        // If we couldn't confirm the booking was created, show a more helpful error
        const helpfulError = ErrorHandler.createError('WEBHOOK_ERROR', 'Your booking was received, but we encountered an issue with our notification system. Your booking is still being processed. You can check your dashboard for updates.');
        throw new Error(helpfulError.message);
      }
      
      throw new Error(supabaseError.message);
    }

    if (!data || data.length === 0) {
      const error = ErrorHandler.createError('NO_DATA_RETURNED', 'No data returned from booking submission');
      ErrorHandler.logError(error);
      throw new Error(error.message);
    }

    ErrorHandler.logSuccess('Booking created successfully', data[0]);

    // If the booking was created successfully, try to manually send the webhook as a backup
    try {
      await sendWebhookNotification(data[0]);
      ErrorHandler.logSuccess('Backup webhook notification sent successfully');
    } catch (webhookError) {
      ErrorHandler.logWarning('Failed to send backup webhook notification', webhookError);
      // Don't throw an error here, as the booking was still created successfully
    }

    return data[0];
  } catch (error) {
    ErrorHandler.logError(ErrorHandler.createError('BOOKING_SUBMISSION_ERROR', 'Error submitting booking form', error));
    
    // Log the error using our error handler
    if (error instanceof Error) {
      const appError = ErrorHandler.createError('BOOKING_SUBMISSION_ERROR', error.message, error);
      ErrorHandler.logError(appError);
    }
    
    throw error;
  }
}

// Helper function to send webhook notifications
async function sendWebhookNotification(bookingData: any) {
  const webhookPayload = {
    id: bookingData.id,
    status: 'sent',
    service_type: bookingData.service_type,
    property_details: bookingData.property_details,
    schedule: bookingData.schedule,
    contact: bookingData.contact,
    created_at: bookingData.created_at
  };

  const success = await sendSecureWebhook(webhookPayload);

  if (!success) {
    throw new Error('Webhook notification failed after all retries');
  }
}

// Helper function to extract service-specific details based on form type
function getServiceSpecificDetails(formData: any, serviceType: string) {
  const details: any = {};

  switch (serviceType) {
    case 'window':
      details.windowDetails = formData.windowDetails || {};
      details.accessDetails = formData.accessDetails || {};
      break;
      
    case 'carpet':
      details.carpetDetails = formData.carpetDetails || {};
      details.stainTreatment = formData.stainTreatment || {};
      break;
      
    case 'residential-carpet':
      details.carpetDetails = formData.carpetDetails || {};
      details.stainTreatment = formData.stainTreatment || {};
      break;
      
    case 'sanitization':
      details.protocolDetails = formData.protocolDetails || {};
      details.sanitizationScope = formData.sanitizationScope || {};
      break;
      
    case 'construction':
      details.projectDetails = formData.propertyDetails || {};
      details.cleaningScope = formData.cleaningScope || {};
      details.debrisDetails = formData.debrisDetails || {};
      break;
      
    case 'tile':
      details.tileDetails = formData.tileDetails || {};
      details.groutCondition = formData.groutCondition || {};
      break;
      
    case 'deep':
      details.cleaningScope = formData.cleaningScope || {};
      details.additionalServices = formData.services?.types || [];
      break;
      
    case 'floor':
      details.floorDetails = formData.floorDetails || {};
      details.restorationScope = formData.restorationScope || {};
      break;
      
    case 'pressure':
      details.surfaceDetails = formData.surfaceDetails || {};
      details.pressureScope = formData.pressureScope || {};
      break;
      
    case 'office':
      details.cleaningDetails = formData.cleaningDetails || {};
      details.services = formData.services || {};
      break;
      
    default:
      details.serviceDetails = formData.serviceDetails || {};
  }

  return details;
}

// Sanitize form data to prevent XSS and injection attacks
function sanitizeFormData(formData: any): any {
  if (!formData || typeof formData !== 'object') {
    return formData;
  }

  const sanitized: any = {};

  for (const [key, value] of Object.entries(formData)) {
    if (typeof value === 'string') {
      // Sanitize string values
      sanitized[key] = InputSanitizer.sanitizeText(value);
    } else if (typeof value === 'object' && value !== null) {
      // Recursively sanitize nested objects
      sanitized[key] = sanitizeFormData(value);
    } else {
      // Keep other types as-is (numbers, booleans, null)
      sanitized[key] = value;
    }
  }

  return sanitized;
}