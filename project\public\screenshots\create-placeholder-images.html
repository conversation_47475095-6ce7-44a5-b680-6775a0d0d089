<!DOCTYPE html>
<html>
<head>
    <title>Create Placeholder Images</title>
</head>
<body>
    <canvas id="desktopCanvas" width="1280" height="720" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <canvas id="mobileCanvas" width="390" height="844" style="border: 1px solid #ccc;"></canvas>
    
    <script>
        // Create desktop screenshot placeholder
        const desktopCanvas = document.getElementById('desktopCanvas');
        const desktopCtx = desktopCanvas.getContext('2d');
        
        // Desktop background
        desktopCtx.fillStyle = '#3b82f6';
        desktopCtx.fillRect(0, 0, 1280, 720);
        
        // Desktop text
        desktopCtx.fillStyle = 'white';
        desktopCtx.font = '48px Arial';
        desktopCtx.textAlign = 'center';
        desktopCtx.fillText('Empire Pro Cleaning', 640, 300);
        desktopCtx.font = '24px Arial';
        desktopCtx.fillText('Professional Cleaning Services', 640, 350);
        desktopCtx.fillText('Desktop View', 640, 400);
        
        // Create mobile screenshot placeholder
        const mobileCanvas = document.getElementById('mobileCanvas');
        const mobileCtx = mobileCanvas.getContext('2d');
        
        // Mobile background
        mobileCtx.fillStyle = '#3b82f6';
        mobileCtx.fillRect(0, 0, 390, 844);
        
        // Mobile text
        mobileCtx.fillStyle = 'white';
        mobileCtx.font = '32px Arial';
        mobileCtx.textAlign = 'center';
        mobileCtx.fillText('Empire Pro', 195, 350);
        mobileCtx.fillText('Cleaning', 195, 390);
        mobileCtx.font = '18px Arial';
        mobileCtx.fillText('Professional Services', 195, 430);
        mobileCtx.fillText('Mobile View', 195, 460);
        
        // Download function
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Auto-download after a short delay
        setTimeout(() => {
            downloadCanvas(desktopCanvas, 'desktop.png');
            setTimeout(() => {
                downloadCanvas(mobileCanvas, 'mobile.png');
            }, 1000);
        }, 1000);
    </script>
    
    <p>This page will automatically download placeholder images for the PWA manifest.</p>
    <p>Move the downloaded files to the public/screenshots/ directory.</p>
</body>
</html>
