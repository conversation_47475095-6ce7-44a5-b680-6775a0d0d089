import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import React from 'react';

// Mock environment variables
const mockEnv = {
  VITE_SUPABASE_URL: 'https://test.supabase.co',
  VITE_SUPABASE_ANON_KEY: 'test-key',
  VITE_SQUARE_APPLICATION_ID: 'test-app-id',
  VITE_SQUARE_ACCESS_TOKEN: 'test-token',
  VITE_SQUARE_LOCATION_ID: 'test-location',
  VITE_SQUARE_ENVIRONMENT: 'sandbox',
  VITE_N8N_WEBHOOK_URL: 'https://test.webhook.com',
  PROD: false
};

// Mock modules
vi.mock('../lib/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ 
            data: { id: 'test-booking-id', status: 'pending' }, 
            error: null 
          }))
        }))
      })),
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => ({
            limit: vi.fn(() => Promise.resolve({ 
              data: [{ id: 'test-booking-id', status: 'pending' }], 
              error: null 
            }))
          }))
        }))
      }))
    })),
    auth: {
      getUser: vi.fn(() => Promise.resolve({ 
        data: { user: { id: 'test-user-id', email: '<EMAIL>' } }, 
        error: null 
      }))
    }
  },
  isSupabaseConfigured: true
}));

vi.mock('../lib/square/config', () => ({
  validateSquareConfig: vi.fn(() => ({
    isValid: true,
    errors: []
  }))
}));

describe('Critical Application Flows', () => {
  beforeEach(() => {
    // Setup environment
    vi.stubGlobal('import.meta', { env: mockEnv });
    
    // Mock console methods
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.unstubAllGlobals();
  });

  describe('Environment Configuration Flow', () => {
    it('should validate environment configuration correctly', async () => {
      const { validateEnvironment } = await import('../lib/config/environment');
      
      const validation = validateEnvironment();
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should handle missing environment variables gracefully', async () => {
      // Test with missing variables
      vi.stubGlobal('import.meta', { 
        env: { ...mockEnv, VITE_SUPABASE_URL: '' } 
      });

      const { validateEnvironment } = await import('../lib/config/environment');
      
      const validation = validateEnvironment();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Payment System Flow', () => {
    it('should initialize payment configuration correctly', async () => {
      const { validateSquareConfig } = await import('../lib/square/config');
      
      const config = validateSquareConfig();
      
      expect(config.isValid).toBe(true);
      expect(config.errors).toHaveLength(0);
    });

    it('should handle payment validation errors', async () => {
      // Mock invalid configuration
      vi.doMock('../lib/square/config', () => ({
        validateSquareConfig: vi.fn(() => ({
          isValid: false,
          errors: ['Invalid Square configuration']
        }))
      }));

      const { validateSquareConfig } = await import('../lib/square/config');
      
      const config = validateSquareConfig();
      
      expect(config.isValid).toBe(false);
      expect(config.errors).toContain('Invalid Square configuration');
    });
  });

  describe('Booking Submission Flow', () => {
    it('should submit booking data successfully', async () => {
      const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
      const mockFormData = {
        propertyDetails: {
          address: '123 Test St',
          propertyType: 'residential'
        },
        serviceDetails: {
          serviceType: 'regular_cleaning',
          frequency: 'weekly'
        },
        schedule: {
          preferredDate: '2024-01-15',
          preferredTime: '10:00'
        },
        contact: {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '555-0123'
        }
      };

      const { BookingService } = await import('../lib/api/bookingService');
      
      const result = await BookingService.saveBooking(
        mockFormData, 
        'residential_regular', 
        mockUser
      );
      
      expect(result).toBeDefined();
      expect(result.id).toBe('test-booking-id');
      expect(result.status).toBe('pending');
    });

    it('should handle booking submission errors gracefully', async () => {
      // Mock Supabase error
      vi.doMock('../lib/supabase/client', () => ({
        supabase: {
          from: vi.fn(() => ({
            insert: vi.fn(() => ({
              select: vi.fn(() => ({
                single: vi.fn(() => Promise.resolve({ 
                  data: null, 
                  error: { message: 'Database error' } 
                }))
              }))
            }))
          }))
        }
      }));

      const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
      const mockFormData = { /* minimal form data */ };

      const { BookingService } = await import('../lib/api/bookingService');
      
      await expect(
        BookingService.saveBooking(mockFormData, 'residential_regular', mockUser)
      ).rejects.toThrow();
    });
  });

  describe('Error Handling Flow', () => {
    it('should handle and log errors correctly', async () => {
      const { ErrorHandler } = await import('../lib/utils/errorHandler');
      
      const testError = ErrorHandler.createError(
        'TEST_ERROR', 
        'Test error message', 
        { detail: 'test detail' }
      );
      
      expect(testError.code).toBe('TEST_ERROR');
      expect(testError.message).toBe('Test error message');
      expect(testError.details).toEqual({ detail: 'test detail' });
      expect(testError.timestamp).toBeInstanceOf(Date);
    });

    it('should sanitize sensitive information in errors', async () => {
      const { ErrorHandler } = await import('../lib/utils/errorHandler');
      
      const sensitiveData = {
        password: 'secret123',
        token: 'abc123token',
        normalField: 'safe data'
      };
      
      const testError = ErrorHandler.createError(
        'SENSITIVE_ERROR', 
        'Error with sensitive data', 
        sensitiveData
      );
      
      expect(testError.details.password).toBe('[REDACTED]');
      expect(testError.details.token).toBe('[REDACTED]');
      expect(testError.details.normalField).toBe('safe data');
    });
  });

  describe('PWA Features Flow', () => {
    it('should initialize PWA manager correctly', async () => {
      // Mock service worker support
      Object.defineProperty(navigator, 'serviceWorker', {
        value: {
          register: vi.fn(() => Promise.resolve({
            addEventListener: vi.fn(),
            installing: null,
            waiting: null,
            active: null
          }))
        },
        configurable: true
      });

      const { pwaManager } = await import('../utils/pwaUtils');
      
      expect(pwaManager).toBeDefined();
      expect(typeof pwaManager.init).toBe('function');
      expect(typeof pwaManager.isInstalled).toBe('function');
    });
  });

  describe('Security Features Flow', () => {
    it('should validate input data correctly', async () => {
      const { FormValidator } = await import('../lib/utils/validation');

      const validData = {
        email: '<EMAIL>',
        phone: '555-0123',
        name: 'Test User'
      };

      const validation = FormValidator.validateContactInfo(validData);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should reject invalid input data', async () => {
      const { FormValidator } = await import('../lib/utils/validation');

      const invalidData = {
        email: 'invalid-email',
        phone: '123', // too short
        name: '' // empty
      };

      const validation = FormValidator.validateContactInfo(invalidData);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete booking to payment flow', async () => {
      // Mock successful booking creation
      const mockBooking = {
        id: 'test-booking-id',
        status: 'pending',
        total_price: 150.00,
        service_type: 'residential_regular'
      };

      // Mock payment link creation
      const mockPaymentLink = {
        id: 'test-payment-link-id',
        url: 'https://square.link/test-payment',
        booking_id: 'test-booking-id'
      };

      // Test the flow
      expect(mockBooking.id).toBeDefined();
      expect(mockPaymentLink.booking_id).toBe(mockBooking.id);
      expect(mockPaymentLink.url).toContain('square.link');
    });

    it('should handle webhook payment status updates', async () => {
      // Mock webhook payload
      const webhookPayload = {
        type: 'payment.updated',
        data: {
          object: {
            payment: {
              id: 'test-payment-id',
              status: 'COMPLETED',
              order_id: 'test-order-id'
            }
          }
        }
      };

      // Test webhook processing
      expect(webhookPayload.type).toBe('payment.updated');
      expect(webhookPayload.data.object.payment.status).toBe('COMPLETED');
    });
  });
});
